import {
  setupE2ETestHooks,
  executeCommand,
  expectNotice,
  expectPostFile
} from '../helpers/shared-context';
import { setupTestFailureHandler } from '../helpers/test-failure-handler';
import { TestPostManager } from '../helpers/ghost-api-helpers';
import { test, describe, beforeAll, afterEach } from 'vitest';
import * as fs from 'fs';
import * as path from 'path';

// Setup screenshot capture on test failures
setupTestFailureHandler();

describe("Commands: Sync All", () => {
  const context = setupE2ETestHooks();
  let postManager: TestPostManager;

  beforeAll(async () => {
    postManager = new TestPostManager();
    if (!postManager.isAvailable()) {
      throw new Error('Ghost API not available - check credentials');
    }
  });

  afterEach(async () => {
    // Clean up managed posts
    await postManager.cleanup();

    // Clean up any local files created during the test
    const articlesDir = path.join(context.vaultPath, 'articles');
    if (fs.existsSync(articlesDir)) {
      const files = fs.readdirSync(articlesDir);
      for (const file of files) {
        if (file.endsWith('.md')) {
          try {
            fs.unlinkSync(path.join(articlesDir, file));
          } catch (error) {
          }
        }
      }
    }
  });

  test("should sync all posts from Ghost using Sync All command", async () => {
    // Create test posts in Ghost using fixtures
    const testFixtures = [
      'sync-all-test-post-1.md',
      'sync-all-test-post-2.md',
      'sync-all-test-post-3.md'
    ];

    // Read fixture content and create posts in Ghost
    for (const fixtureName of testFixtures) {
      const fixtureContent = fs.readFileSync(
        path.resolve(`tests/fixtures/${fixtureName}`),
        'utf8'
      );

      // Parse frontmatter and content
      const frontmatterMatch = fixtureContent.match(/^---\n([\s\S]*?)\n---\n([\s\S]*)$/);
      if (!frontmatterMatch) {
        throw new Error(`Invalid fixture format: ${fixtureName}`);
      }

      const frontmatterText = frontmatterMatch[1];
      const markdownContent = frontmatterMatch[2];

      // Parse frontmatter
      const frontmatter: any = {};
      frontmatterText.split('\n').forEach(line => {
        const [key, ...valueParts] = line.split(':');
        if (key && valueParts.length > 0) {
          const value = valueParts.join(':').trim();
          if (key === 'tags') {
            // Handle tags array
            frontmatter[key] = [];
          } else {
            frontmatter[key] = value.replace(/^["']|["']$/g, '');
          }
        }
      });

      // Handle tags separately (they're in YAML array format)
      const tagsMatch = frontmatterText.match(/tags:\s*\n((?:\s*-\s*.+\n?)*)/);
      if (tagsMatch) {
        const tagLines = tagsMatch[1].trim().split('\n');
        frontmatter.tags = tagLines.map(line => ({ name: line.replace(/^\s*-\s*/, '') }));
      }

      // Convert markdown to lexical format using the proper converter
      const { markdownToLexical } = await import('../../src/markdown/index.js');
      const lexicalResult = await markdownToLexical(markdownContent);

      let lexicalContent;
        // Create the proper lexical structure that Ghost expects
        lexicalContent = {
          root: {
            ...lexicalResult.data.root,
            type: 'root' // Ensure the root has the correct type
          }
        };
      }

      // Create post in Ghost
      const postData = {
        title: frontmatter.title,
        slug: frontmatter.slug,
        lexical: JSON.stringify(lexicalContent),
        status: frontmatter.status,
        visibility: frontmatter.visibility,
        tags: frontmatter.tags || []
      };

      const createdPost = await postManager.getAPI().createPost(postData);
    }

    // Wait a moment for Ghost to process the posts
    await new Promise(resolve => setTimeout(resolve, 1000));

    await executeCommand(context, 'Ghost Sync: Sync all from Ghost');

    // Wait for the sync operation to complete with longer timeout for sync-all operations
    await expectNotice(context, "Successfully synced", 15000);

    // Give a moment for files to be written to disk
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Verify each post was created as a local file with FULL content
    await expectPostFile(context, "sync-all-test-post-1", {
      title: "Sync All Test Post 1 - Comprehensive Content",
      slug: "sync-all-test-post-1",
      status: "published",
      visibility: "public",
      tags: ["test", "sync-all", "comprehensive"],
      content: "# Sync All Test Post 1 - Comprehensive Content"
    });

    // Check for multiple paragraphs and sections in the first post
    await expectPostFile(context, "sync-all-test-post-1", {
      content: "This is the first test post for the sync-all command functionality"
    });

    await expectPostFile(context, "sync-all-test-post-1", {
      content: "## Introduction"
    });

    await expectPostFile(context, "sync-all-test-post-1", {
      content: "## Multiple Paragraphs"
    });

    await expectPostFile(context, "sync-all-test-post-1", {
      content: "**End of comprehensive test content.**"
    });

    // Verify second post with extended formatting
    await expectPostFile(context, "sync-all-test-post-2", {
      title: "Sync All Test Post 2 - Extended Formatting",
      slug: "sync-all-test-post-2",
      status: "published",
      visibility: "public",
      tags: ["test", "sync-all", "formatting"],
      content: "# Sync All Test Post 2 - Extended Formatting"
    });

    // Check for complex formatting elements
    await expectPostFile(context, "sync-all-test-post-2", {
      content: "**bold text**, *italic text*, ***bold and italic***"
    });

    await expectPostFile(context, "sync-all-test-post-2", {
      content: "| Column 1 | Column 2 | Column 3 |"
    });

    await expectPostFile(context, "sync-all-test-post-2", {
      content: "**End of extended formatting test.**"
    });

    // Verify third post with complex structure
    await expectPostFile(context, "sync-all-test-post-3", {
      title: "Sync All Test Post 3 - Complex Structure",
      slug: "sync-all-test-post-3",
      status: "published",
      visibility: "public",
      tags: ["test", "sync-all", "structure"],
      content: "# Sync All Test Post 3 - Complex Structure"
    });

    // Check for complex document structure
    await expectPostFile(context, "sync-all-test-post-3", {
      content: "## Document Structure Overview"
    });

    await expectPostFile(context, "sync-all-test-post-3", {
      content: "### Subsection 1: Content Depth"
    });

    await expectPostFile(context, "sync-all-test-post-3", {
      content: "**This marks the end of the complex structure test post.**"
    });
  });
});
